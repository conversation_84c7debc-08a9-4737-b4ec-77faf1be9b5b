'use strict';

// Language translations for reCAPTCHA Audio Solver
const translations = {
  en: {
    // Popup UI
    title: "reCAPTCHA Audio Solver",
    toggleActive: "Enabled",
    toggleInactive: "Disabled",
    statusLabel: "Status:",
    successLabel: "Solved:",
    failLabel: "Failed:",
    authorCredit: "by |Moryata|",

    // Status messages
    loading: "Loading...",
    active: "Active and waiting for reCAPTCHA",
    inactive: "Inactive. Toggle to enable.",
    processing: "Processing audio...",
    success: "Successfully solved!",
    botDetected: "Bot detected. reCAPTCHA stopped for this frame.",

    // Time formatting
    justNow: "Just now",
    secondsAgo: "s ago",
    minutesAgo: "m ago",
    noData: "--",

    // Settings & Performance
    settings: "Settings",
    darkMode: "Dark Mode",
    language: "Language",
    performance: "Performance (ms)",
    injection: "Injection:",
    detection: "Detection:",
    processingTime: "Processing:",
    debugMode: "Debug Mode"
  },
  id: {
    // Popup UI
    title: "reCAPTCHA Audio Solver",
    toggleActive: "Diaktifkan",
    toggleInactive: "Dinonaktifkan",
    statusLabel: "Status:",
    successLabel: "Berhasil:",
    failLabel: "Gagal:",
    authorCredit: "oleh |Moryata|",

    // Status messages
    loading: "Memuat...",
    active: "Aktif dan menunggu reCAPTCHA",
    inactive: "Nonaktif. Aktifkan untuk memulai.",
    processing: "Memproses audio...",
    success: "Berhasil diselesaikan!",
    botDetected: "Bot terdeteksi. reCAPTCHA dihentikan untuk frame ini.",

    // Time formatting
    justNow: "Baru saja",
    secondsAgo: "d lalu",
    minutesAgo: "m lalu",
    noData: "--",

    // Settings & Performance
    settings: "Pengaturan",
    darkMode: "Mode Gelap",
    language: "Bahasa",
    performance: "Performa (md)",
    injection: "Injeksi:",
    detection: "Deteksi:",
    processingTime: "Proses:",
    debugMode: "Mode Debug"
  },
  es: {
    // Popup UI
    title: "reCAPTCHA Audio Solver",
    toggleActive: "Activado",
    toggleInactive: "Desactivado",
    statusLabel: "Estado:",
    successLabel: "Resuelto:",
    failLabel: "Fallido:",
    authorCredit: "por |Moryata|",

    // Status messages
    loading: "Cargando...",
    active: "Activo y esperando reCAPTCHA",
    inactive: "Inactivo. Activa para empezar.",
    processing: "Procesando audio...",
    success: "¡Resuelto con éxito!",
    botDetected: "Bot detectado. reCAPTCHA detenido para este frame.",

    // Time formatting
    justNow: "Ahora mismo",
    secondsAgo: "s atrás",
    minutesAgo: "m atrás",
    noData: "--",

    // Settings & Performance
    settings: "Configuración",
    darkMode: "Modo Oscuro",
    language: "Idioma",
    performance: "Rendimiento (ms)",
    injection: "Inyección:",
    detection: "Detección:",
    processingTime: "Procesando:",
    debugMode: "Modo Debug"
  }
};

// --- Language Management ---
let currentLang = 'id'; // Default to Indonesian

function t(key) {
  return translations[currentLang]?.[key] || translations.en?.[key] || key;
}

function setLanguage(lang) {
  if (!translations[lang]) return false;
  currentLang = lang;
  try {
    localStorage.setItem('reCAPTCHA_solver_lang', lang);
  } catch (e) {
    console.debug('Failed to save language preference:', e);
  }
  return true;
}

function getLanguage() {
  return currentLang;
}

function initLanguage() {
  try {
    const storedLang = localStorage.getItem('reCAPTCHA_solver_lang');
    if (storedLang && translations[storedLang]) {
      currentLang = storedLang;
    }
  } catch (e) {
    console.debug('Failed to load language preference:', e);
  }
}

// Initialize language when the script is first loaded
initLanguage();
