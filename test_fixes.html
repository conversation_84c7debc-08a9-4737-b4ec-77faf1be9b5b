<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reCAPTCHA Audio Solver - Test Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #test-results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>reCAPTCHA Audio Solver - Test Fixes</h1>
    
    <div class="test-section">
        <h2>Test Environment</h2>
        <p>This page tests the fixes applied to the content.js file.</p>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="test-results"></div>

    <script>
        // Mock Chrome APIs for testing
        window.chrome = {
            runtime: {
                sendMessage: function(message, callback) {
                    setTimeout(() => {
                        if (callback) callback({ success: true });
                    }, 100);
                },
                onMessage: {
                    addListener: function(listener) {
                        console.log('Message listener added');
                    }
                },
                lastError: null
            },
            storage: {
                local: {
                    get: function(keys, callback) {
                        setTimeout(() => {
                            callback({
                                solveCount: 5,
                                failCount: 2,
                                enabled: true
                            });
                        }, 50);
                    },
                    set: function(data, callback) {
                        setTimeout(() => {
                            if (callback) callback();
                        }, 50);
                    }
                }
            }
        };

        // Mock reCAPTCHA constants
        window.RECAPTCHA_CONSTANTS = {
            VERSION_INFO: { VERSION: '3.8.1' },
            ATTEMPT_LIMITS: { MAX_ATTEMPTS_PER_SERVER: 3, MAX_TOTAL_ATTEMPTS: 5 },
            PERFORMANCE_CONFIG: {
                POLLING_INTERVAL_MS: 500,
                CACHE_DURATION_MS: 2000,
                MESSAGE_TIMEOUT_MS: 15000,
                DEBOUNCE_STATUS_UPDATE_MS: 200,
                MUTATION_OBSERVER_DEBOUNCE_MS: 50
            },
            SELECTORS: {
                CHECKBOX: '.recaptcha-checkbox-border',
                AUDIO_BUTTON: '#recaptcha-audio-button',
                IMAGE_BUTTON: '#recaptcha-image-button',
                IMAGE_SELECT: '#rc-imageselect',
                AUDIO_SOURCE: '#audio-source',
                RESPONSE_FIELD: '.rc-audiochallenge-response-field',
                AUDIO_RESPONSE: '#audio-response',
                AUDIO_ERROR_MESSAGE: '.rc-audiochallenge-error-message',
                RELOAD_BUTTON: '#recaptcha-reload-button',
                DOSCAPTCHA: '.rc-doscaptcha-body',
                VERIFY_BUTTON: '#recaptcha-verify-button'
            },
            SERVERS: [
                'https://engageub.pythonanywhere.com',
                'https://engageub1.pythonanywhere.com'
            ]
        };

        let testResults = [];

        function addTestResult(testName, passed, message) {
            testResults.push({ testName, passed, message });
            displayResults();
        }

        function displayResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h2>Test Results</h2>';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.passed ? 'pass' : 'fail'}`;
                div.innerHTML = `
                    <strong>${result.testName}</strong>: ${result.passed ? 'PASS' : 'FAIL'}
                    <br><small>${result.message}</small>
                `;
                resultsDiv.appendChild(div);
            });
        }

        function clearResults() {
            testResults = [];
            document.getElementById('test-results').innerHTML = '';
        }

        function runAllTests() {
            clearResults();
            
            // Test 1: URL Validation
            try {
                // This should pass
                const validUrl = 'https://www.google.com/recaptcha/api2/payload/audio.mp3';
                const isValid = testUrlValidation(validUrl);
                addTestResult('URL Validation - Valid URL', isValid, 'Valid reCAPTCHA URL accepted');
                
                // This should fail
                const invalidUrl = 'javascript:alert("xss")';
                const isInvalid = !testUrlValidation(invalidUrl);
                addTestResult('URL Validation - Invalid URL', isInvalid, 'Invalid URL rejected');
            } catch (error) {
                addTestResult('URL Validation', false, `Error: ${error.message}`);
            }

            // Test 2: Element Caching
            try {
                const testElement = document.createElement('div');
                testElement.id = 'test-element';
                document.body.appendChild(testElement);
                
                // Test caching functionality
                const cached1 = testElementCaching('#test-element');
                const cached2 = testElementCaching('#test-element');
                const isCached = cached1 === cached2;
                
                document.body.removeChild(testElement);
                addTestResult('Element Caching', isCached, 'Element caching works correctly');
            } catch (error) {
                addTestResult('Element Caching', false, `Error: ${error.message}`);
            }

            // Test 3: Input Validation
            try {
                const validInput = testInputValidation('solve', 'Test message');
                const invalidInput1 = testInputValidation('invalid', 'Test message');
                const invalidInput2 = testInputValidation('solve', null);
                
                const passed = validInput && !invalidInput1 && !invalidInput2;
                addTestResult('Input Validation', passed, 'Input validation working correctly');
            } catch (error) {
                addTestResult('Input Validation', false, `Error: ${error.message}`);
            }

            // Test 4: Error Handling
            try {
                const errorHandled = testErrorHandling();
                addTestResult('Error Handling', errorHandled, 'Error handling implemented correctly');
            } catch (error) {
                addTestResult('Error Handling', false, `Error: ${error.message}`);
            }
        }

        function testUrlValidation(url) {
            try {
                const urlObj = new URL(url);
                if (!['http:', 'https:'].includes(urlObj.protocol)) {
                    return false;
                }
                if (!urlObj.hostname.includes('google.com') && !urlObj.hostname.includes('recaptcha.net')) {
                    return false;
                }
                return true;
            } catch (error) {
                return false;
            }
        }

        function testElementCaching(selector) {
            // Simplified version of the caching logic
            const cache = new Map();
            
            if (cache.has(selector)) {
                return cache.get(selector);
            }
            
            const element = document.querySelector(selector);
            if (element && element.isConnected) {
                cache.set(selector, element);
            }
            return element;
        }

        function testInputValidation(type, status) {
            if (!['solve', 'fail'].includes(type)) {
                return false;
            }
            if (!status || typeof status !== 'string') {
                return false;
            }
            return true;
        }

        function testErrorHandling() {
            // Test that error handling functions exist and work
            try {
                // Simulate error conditions
                const mockError = new Error('Test error');
                console.log('Error handling test:', mockError.message);
                return true;
            } catch (error) {
                return false;
            }
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
