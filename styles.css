:root {
  --bg-color: #f5f5f5;
  --container-bg: #ffffff;
  --text-color: #333333;
  --text-secondary: #5f6368;
  --border-color: #e8eaed;
  --stat-success-bg: linear-gradient(135deg, #ffffff, #e8f5e9);
  --stat-fail-bg: linear-gradient(135deg, #ffffff, #ffebee);
  --status-container-bg: #f8f9fa;
  --toggle-bg: rgba(0, 0, 0, 0.2);
  --toggle-handle: white;
  --bottom-container-bg: #f8f9fa;
  --lang-btn-bg: #ffffff;
  --lang-btn-color: #5f6368;
  --lang-btn-border: #e0e0e0;
  --lang-btn-active-bg: #e8f0fe;
  --lang-btn-active-color: #4285F4;
  --lang-btn-active-border: #4285F4;
  --shadow-color: rgba(0, 0, 0, 0.12);
  --shadow-small: rgba(0, 0, 0, 0.08);
  --performance-bg: #f8f9fa;
  --performance-border: #e8eaed;
  --metric-bg: #ffffff;
  --debug-btn-bg: #f1f3f4;
  --debug-btn-active: #4285F4;
}

[data-theme="dark"] {
  --bg-color: #202124;
  --container-bg: #292a2d;
  --text-color: #e8eaed;
  --text-secondary: #9aa0a6;
  --border-color: #3c4043;
  --stat-success-bg: linear-gradient(135deg, #292a2d, #0d3320);
  --stat-fail-bg: linear-gradient(135deg, #292a2d, #3d1c1c);
  --status-container-bg: #202124;
  --toggle-bg: rgba(255, 255, 255, 0.2);
  --toggle-handle: #e8eaed;
  --bottom-container-bg: #202124;
  --lang-btn-bg: #3c4043;
  --lang-btn-color: #e8eaed;
  --lang-btn-border: #5f6368;
  --lang-btn-active-bg: #174ea6;
  --lang-btn-active-color: #e8eaed;
  --lang-btn-active-border: #8ab4f8;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-small: rgba(0, 0, 0, 0.2);
  --performance-bg: #1a1a1a;
  --performance-border: #404040;
  --metric-bg: #333333;
  --debug-btn-bg: #404040;
  --debug-btn-active: #4285F4;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Product Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
  width: 320px;
  padding: 0 0 5px 0;
  background-color: var(--container-bg);
  box-shadow: 0 3px 12px var(--shadow-color);
  border-radius: 8px;
  overflow: hidden;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  animation: container-appear 0.5s ease-out;
}

@keyframes container-appear {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 20px;
  position: relative;
  background: var(--container-bg);
  margin-bottom: 0;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

/* Bottom container and language selector */
.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.lang-selector {
  display: flex;
  gap: 5px;
  justify-content: center;
  z-index: 10;
}

.lang-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid var(--lang-btn-border);
  background-color: var(--lang-btn-bg);
  color: var(--lang-btn-color);
  font-weight: 600;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: 0 1px 3px var(--shadow-small);
  position: relative;
  overflow: hidden;
}

.lang-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.lang-btn:hover {
  background-color: var(--lang-btn-bg);
  transform: scale(1.05);
  box-shadow: 0 2px 5px var(--shadow-small);
}

.lang-btn:hover::before {
  width: 100%;
  height: 100%;
}

.lang-btn:active {
  transform: scale(0.95);
}

.lang-btn.lang-active {
  border-color: var(--lang-btn-active-border);
  color: var(--lang-btn-active-color);
  background-color: var(--lang-btn-active-bg);
  box-shadow: 0 2px 5px var(--shadow-small);
  transform: scale(1.05);
}

/* Dark mode toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.theme-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-secondary);
  transition: fill 0.3s ease;
}

.header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 25%,
    #34A853 25%,
    #34A853 50%,
    #FBBC05 50%,
    #FBBC05 75%,
    #EA4335 75%,
    #EA4335 100%
  );
}

.header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 25%,
    #34A853 25%,
    #34A853 50%,
    #FBBC05 50%,
    #FBBC05 75%,
    #EA4335 75%,
    #EA4335 100%
  );
}

h1 {
  font-size: 20px;
  color: var(--text-color);
  margin-bottom: 12px;
  text-align: center;
  font-weight: 500;
  transition: color 0.3s ease;
}

.toggle-container {
  display: flex;
  align-items: center;
  margin-top: 5px;
  background-color: var(--status-container-bg);
  padding: 6px 12px;
  border-radius: 20px;
  box-shadow: 0 1px 3px var(--shadow-small);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

#toggleStatus {
  margin-left: 10px;
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--toggle-bg);
  transition: .3s;
  box-shadow: inset 0 1px 3px var(--shadow-small);
  overflow: hidden;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: var(--toggle-handle);
  transition: .3s;
  box-shadow: 0 1px 3px var(--shadow-small);
  z-index: 2;
}

.slider:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 33%,
    #34A853 33%,
    #34A853 67%,
    #FBBC05 67%,
    #FBBC05 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

input:checked + .slider {
  background-color: transparent;
}

input:checked + .slider:after {
  opacity: 1;
}

input:focus + .slider {
  box-shadow: 0 0 3px #4285F4;
}

input:checked + .slider:before {
  transform: translateX(26px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.slider.round {
  border-radius: 26px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Status */
.status-container {
  display: flex;
  align-items: center;
  margin: 20px;
  padding: 12px 15px;
  background-color: var(--status-container-bg);
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--shadow-small);
  border-left: 4px solid #4285F4;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 100%);
  z-index: 0;
}

.status-container > * {
  position: relative;
  z-index: 1;
}

/* Status icons */
.status-container.active::after {
  content: "✓";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #34A853;
}

.status-container.inactive::after {
  content: "○";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #EA4335;
}

.status-container.processing::after {
  content: "⟳";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #FBBC05;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* Bot detection status styling */
.status-container.bot-detected {
  border-left: 4px solid #FF0000;
  background-color: rgba(255, 0, 0, 0.1);
  position: relative;
  padding-right: 40px;
}

.status-container.bot-detected::after {
  content: "⚠️";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  animation: pulse-warning 2s infinite;
}

[data-theme="dark"] .status-container.bot-detected {
  background-color: rgba(255, 0, 0, 0.2);
}

@keyframes pulse-warning {
  0% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
}



/* Loading state styling */
.status-container.loading {
  border-left: 4px solid #FBBC05;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.status-label {
  font-weight: 600;
  margin-right: 10px;
  color: #4285F4;
  transition: color 0.3s ease;
}

.status-value {
  color: var(--text-color);
  flex: 1;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Stats */
.stats-container {
  display: flex;
  justify-content: space-between;
  margin: 0 20px 20px;
}

.stat {
  flex: 1;
  padding: 15px 10px;
  border-radius: 8px;
  text-align: center;
  margin: 0 5px;
  box-shadow: 0 1px 3px var(--shadow-small);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px var(--shadow-small);
}

.stat:hover::after {
  opacity: 1;
}

.stat:first-child {
  margin-left: 0;
  background: var(--stat-success-bg);
  border-bottom: 3px solid #34A853;
}

.stat:last-child {
  margin-right: 0;
  background: var(--stat-fail-bg);
  border-bottom: 3px solid #EA4335;
}

.stat-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.stat-value {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-color);
  transition: color 0.3s ease;
}

#solveCount {
  color: #34A853;
  transition: color 0.3s ease;
}

[data-theme="dark"] #solveCount {
  color: #4AE371; /* Brighter green for dark mode */
}

#failCount {
  color: #EA4335;
  transition: color 0.3s ease;
}

[data-theme="dark"] #failCount {
  color: #FF6B5E; /* Brighter red for dark mode */
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
  padding: 12px 20px;
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.version {
  font-weight: 500;
}

.author {
  font-weight: 500;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.author-name {
  font-weight: 700;
  color: #4285F4;
  font-size: 14px;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  padding: 0 2px;
  text-shadow: 1px 1px 1px var(--shadow-small);
  transition: all 0.3s ease;
}

[data-theme="dark"] .author-name {
  color: #5C9CFF; /* Brighter blue for dark mode */
}

.author-name:hover {
  color: #EA4335;
  transform: scale(1.05);
}

/* Performance Container Styles */
.performance-container {
  margin: 12px 0;
  padding: 12px;
  background: var(--performance-bg);
  border: 1px solid var(--performance-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.performance-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.debug-toggle {
  background: var(--debug-btn-bg);
  border: none;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-toggle:hover {
  background: var(--debug-btn-active);
  transform: scale(1.05);
}

.debug-toggle.active {
  background: var(--debug-btn-active);
  box-shadow: 0 0 8px rgba(66, 133, 244, 0.3);
}

.debug-icon {
  width: 14px;
  height: 14px;
  fill: var(--text-color);
  transition: fill 0.2s ease;
}

.debug-toggle:hover .debug-icon,
.debug-toggle.active .debug-icon {
  fill: white;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.metric {
  background: var(--metric-bg);
  border-radius: 6px;
  padding: 8px 6px;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.metric:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px var(--shadow-small);
}

.metric-label {
  font-size: 10px;
  color: var(--text-secondary);
  margin-bottom: 2px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color);
  font-family: 'Courier New', monospace;
}

.metric-value:not(:empty):not([data-value="-"]) {
  color: #4285F4;
}

/* Animation for performance container */
.performance-container.show {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Auto Handle Container */
