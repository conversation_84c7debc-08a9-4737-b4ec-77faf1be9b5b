'use strict';

/**
 * reCAPTCHA Audio Solver v3.8.1
 * Background script - optimized version
 */

// Initialize extension state
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.set({
    enabled: true,
    solveCount: 0,
    failCount: 0,
    lastStatus: "Active",
    botDetected: false,
    lastUpdated: Date.now()
  });
  console.log('reCAPTCHA Audio Solver v3.8.1 extension installed');

  // Set badge color
  chrome.action.setBadgeBackgroundColor({ color: '#4CAF50' });
});

// Optimized cookie deletion function with better error handling and performance
async function deleteCookiesForRecaptcha() {
  console.log('[reCAPTCHA Audio Solver] Deleting cookies for recaptcha.net domain');

  try {
    // Optimized domain list - only target necessary domains
    const domains = [
      '.recaptcha.net',
      'www.recaptcha.net',
      '.google.com',
      'www.google.com'
    ];

    // Get all cookies in parallel with timeout protection
    const cookiePromises = domains.map(domain =>
      chrome.cookies.getAll({ domain }).catch(() => [])
    );

    const cookieArrays = await Promise.all(cookiePromises);
    const [recaptchaCookies, wwwRecaptchaCookies, googleCookies, wwwGoogleCookies] = cookieArrays;

    // More efficient filtering for Google cookies
    const relevantGoogleCookies = [...googleCookies, ...wwwGoogleCookies].filter(
      cookie => {
        const name = cookie.name.toLowerCase();
        const path = cookie.path.toLowerCase();
        return name.includes('captcha') || path.includes('recaptcha');
      }
    );

    // Combine all cookies
    const allCookies = [...recaptchaCookies, ...wwwRecaptchaCookies, ...relevantGoogleCookies];

    if (allCookies.length === 0) {
      return { success: true, count: 0 };
    }

    // Optimized deletion with better batching
    const batchSize = allCookies.length <= 5 ? 1 : 8; // Smaller batches for better reliability
    const deletionPromises = [];

    for (let i = 0; i < allCookies.length; i += batchSize) {
      const batch = allCookies.slice(i, i + batchSize);
      const batchPromise = Promise.all(batch.map(cookie =>
        chrome.cookies.remove({
          url: `https://${cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain}${cookie.path}`,
          name: cookie.name
        }).catch(() => null) // Ignore individual failures
      ));
      deletionPromises.push(batchPromise);
    }

    await Promise.all(deletionPromises);

    return { success: true, count: allCookies.length };
  } catch (error) {
    console.error('[reCAPTCHA Audio Solver] Error deleting cookies:', error);
    return { success: false, error: error.message };
  }
}

// Process audio from server - optimized version with centralized constants
async function processAudioFromServer(audioUrl, serverUrl) {
  const logPrefix = '[reCAPTCHA Audio Solver]';
  console.log(`${logPrefix} Processing audio...`);

  try {
    // User agents array - optimized and centralized
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.75 Safari/537.36'
    ];

    // Get a random user agent
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

    // Request headers - defined once
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'text/plain',
      'User-Agent': userAgent,
      'Origin': chrome.runtime.getURL(''),
      'Cache-Control': 'no-cache'
    };

    // Make the request to the audio processing server with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 12000); // 12 second timeout

    const response = await fetch(serverUrl, {
      method: 'POST',
      headers,
      body: 'input=' + encodeURIComponent(audioUrl) + '&lang=en',
      credentials: 'omit',
      mode: 'cors',
      cache: 'no-store',
      redirect: 'follow',
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    // Handle non-OK responses
    if (!response.ok) {
      const errorMsg = `Server error: ${response.status}`;
      console.error(`${logPrefix} ${errorMsg}`); // Keep error logs
      return {
        success: false,
        error: errorMsg,
        status: response.status
      };
    }

    const text = await response.text();

    // Combined validation check
    const isInvalid = !text || /<|>/.test(text) || text.length > 50;
    const isTooShort = text.length < 4 && !/^\d$/.test(text);

    if (isInvalid) {
      console.error(`${logPrefix} Invalid response`); // Keep error logs but don't show the text
      return {
        success: false,
        error: 'Invalid response from server'
      };
    }

    if (isTooShort) {
      console.error(`${logPrefix} Response too short`); // Keep error logs but don't show the text
      return {
        success: false,
        error: 'Response too short'
      };
    }

    // Log the successful answer - this is important to keep
    console.log(`${logPrefix} Successfully processed audio: ${text}`);
    return {
      success: true,
      text
    };

  } catch (error) {
    // Handle different types of errors
    if (error.name === 'AbortError') {
      console.error(`${logPrefix} Request timeout`);
      return {
        success: false,
        error: 'Request timeout',
        errorType: 'Timeout'
      };
    }

    // Keep error logs but simplify them
    console.error(`${logPrefix} Error processing audio: ${error.message || 'Unknown error'}`);
    return {
      success: false,
      error: error.message || 'Unknown error',
      errorType: error.name || 'Error'
    };
  }
}

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  // Using _sender to indicate we're aware of the parameter but not using it

  // Handle status updates
  if (request.action === "updateStatus") {
    if (request.stats) {
      if (request.stats.solved > 0) {
        chrome.action.setBadgeText({ text: request.stats.solved.toString() });
      }

      // Update badge color based on enabled state
      const badgeColor = request.stats.enabled ? '#4CAF50' : '#EA4335';
      chrome.action.setBadgeBackgroundColor({ color: badgeColor });

      // Update storage with latest stats
      chrome.storage.local.set({
        solveCount: request.stats.solved || 0,
        failCount: request.stats.failed || 0,
        lastStatus: request.status,
        lastUpdated: Date.now()
      });
    }
    return false;
  }

  // Helper function to safely send response
  const safelySendResponse = (response) => {
    try {
      if (!chrome.runtime.lastError) {
        sendResponse(response);
      } else {
        // Only log critical errors
        if (chrome.runtime.lastError.message !== "The message port closed before a response was received.") {
          console.warn(`[reCAPTCHA Audio Solver] Message port closed: ${chrome.runtime.lastError.message}`);
        }
      }
    } catch (error) {
      // Only log actual errors, not expected conditions
      console.error(`[reCAPTCHA Audio Solver] Error sending response: ${error.message}`);
    }
  };

  // Handle delete cookies request
  if (request.action === "deleteCookies") {
    (async () => {
      try {
        const result = await deleteCookiesForRecaptcha();
        safelySendResponse(result);
      } catch (error) {
        safelySendResponse({
          success: false,
          error: error.message || 'Unknown error'
        });
      }
    })();

    return true; // Indicates that sendResponse will be called asynchronously
  }

  // Process audio request
  if (request.action === "processAudio") {
    // Enhanced input validation for security
    if (!request.audioUrl || !request.serverUrl ||
        typeof request.audioUrl !== 'string' ||
        typeof request.serverUrl !== 'string') {
      sendResponse({
        success: false,
        error: 'Invalid or missing required parameters'
      });
      return false;
    }

    // Validate URL format for security
    try {
      new URL(request.audioUrl);
      new URL(request.serverUrl);
    } catch (error) {
      sendResponse({
        success: false,
        error: 'Invalid URL format provided'
      });
      return false;
    }

    // Process the audio
    (async () => {
      try {
        const result = await processAudioFromServer(request.audioUrl, request.serverUrl);
        safelySendResponse(result);
      } catch (error) {
        safelySendResponse({
          success: false,
          error: error.message || 'Unknown error'
        });
      }
    })();

    return true; // Indicates that sendResponse will be called asynchronously
  }

  return false; // For other unhandled requests
});
