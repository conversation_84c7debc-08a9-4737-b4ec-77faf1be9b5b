'use strict';

/**
 * reCAPTCHA Audio Solver v3.8.1
 * Popup script - Refactored for clarity and maintainability
 */

document.addEventListener('DOMContentLoaded', () => {
  // --- Element Cache - Optimized to only cache existing elements ---
  const ui = {
    solverToggle: document.getElementById('solverToggle'),
    toggleStatus: document.getElementById('toggleStatus'),
    status: document.getElementById('status'),
    solveCount: document.getElementById('solveCount'),
    failCount: document.getElementById('failCount'),
    statusContainer: document.querySelector('.status-container'),
    themeToggle: document.getElementById('themeToggle'),
    themeIcon: document.getElementById('themeIcon'),
    langEN: document.getElementById('langEN'),
    langID: document.getElementById('langID'),
    langES: document.getElementById('langES'),
  };

  // --- State ---
  let currentStats = { solveCount: 0, failCount: 0 };

  // --- Animations ---
  function animateValue(element, start, end, duration = 400) {
    if (start === end) return;
    const startTime = performance.now();
    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = 1 - Math.pow(1 - progress, 3); // EaseOutCubic
      element.textContent = Math.floor(easedProgress * (end - start) + start);
      if (progress < 1) requestAnimationFrame(animate);
    };
    requestAnimationFrame(animate);
  }

  function fadeUpdate(element, text) {
    if (element.textContent === text) return;
    element.style.opacity = '0';
    setTimeout(() => {
      element.textContent = text;
      element.style.opacity = '1';
    }, 150);
  }

  // --- UI Update Function ---
  function updateUI(data) {
    // Update toggle and main status
    ui.solverToggle.checked = data.enabled;

    let statusKey = data.enabled ? 'active' : 'inactive';
    let statusClass = data.enabled ? 'active' : 'inactive';

    // Handle different status states with better detection
    if (data.botDetected && data.lastStatus &&
        (data.lastStatus.includes('Bot terdeteksi') || data.lastStatus.includes('Bot detected'))) {
      statusKey = 'botDetected';
      statusClass = 'bot-detected';
    } else if (data.lastStatus &&
               (data.lastStatus.includes('Processing') ||
                data.lastStatus.includes('Memproses') ||
                data.lastStatus.includes('Procesando'))) {
      statusKey = 'processing';
      statusClass = 'processing';
    }

    fadeUpdate(ui.status, t(statusKey));
    ui.toggleStatus.textContent = t(data.enabled ? 'toggleActive' : 'toggleInactive');

    // Update container style
    ui.statusContainer.className = `status-container ${statusClass}`;

    // Update counters
    animateValue(ui.solveCount, currentStats.solveCount, data.solveCount);
    animateValue(ui.failCount, currentStats.failCount, data.failCount);
    currentStats = { solveCount: data.solveCount, failCount: data.failCount };
  }

  // --- Language & Theme ---
  function applyTranslations() {
    document.querySelectorAll('[data-lang-key]').forEach(el => {
      el.textContent = t(el.dataset.langKey);
    });
    // Re-apply dynamic text
    chrome.storage.local.get(['enabled', 'botDetected'], (data) => {
        ui.toggleStatus.textContent = t(data.enabled ? 'toggleActive' : 'toggleInactive');
        const statusKey = data.botDetected ? 'botDetected' : (data.enabled ? 'active' : 'inactive');
        ui.status.textContent = t(statusKey);
    });
  }

  function setupLanguageSwitcher() {
    const buttons = { en: ui.langEN, id: ui.langID, es: ui.langES };
    const currentLang = getLanguage();

    Object.values(buttons).forEach(btn => btn.classList.remove('lang-active'));
    if(buttons[currentLang]) buttons[currentLang].classList.add('lang-active');

    Object.entries(buttons).forEach(([lang, button]) => {
      button.addEventListener('click', () => {
        if (getLanguage() === lang) return;
        setLanguage(lang);
        Object.values(buttons).forEach(b => b.classList.remove('lang-active'));
        button.classList.add('lang-active');
        applyTranslations();
      });
    });
  }

  function setupTheme() {
    const lightIcon = '<path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" />';
    const darkIcon = '<path d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />';

    const applyTheme = (theme) => {
      document.body.dataset.theme = theme;
      ui.themeIcon.innerHTML = theme === 'dark' ? darkIcon : lightIcon;
      try { localStorage.setItem('reCAPTCHA_solver_theme', theme); } catch(e) {}
    };

    ui.themeToggle.addEventListener('click', () => {
      applyTheme(document.body.dataset.theme === 'dark' ? 'light' : 'dark');
    });

    applyTheme(localStorage.getItem('reCAPTCHA_solver_theme') || 'light');
  }

  // --- Event Handlers ---
  function setupEventListeners() {
    ui.solverToggle.addEventListener('change', () => {
      const enabled = ui.solverToggle.checked;
      chrome.storage.local.set({ enabled });
      // The storage.onChanged listener will handle the UI update
    });



    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace !== 'local') return;
      // Re-fetch all data to ensure UI consistency
      loadInitialData();
    });
  }

  // --- Initial Load ---
  function loadInitialData() {
    ui.statusContainer.classList.add('loading');
    chrome.storage.local.get(['enabled', 'solveCount', 'failCount', 'botDetected', 'lastStatus'], (data) => {
      if (chrome.runtime.lastError) return;
      const fullData = {
        enabled: data.enabled !== undefined ? data.enabled : true,
        solveCount: data.solveCount || 0,
        failCount: data.failCount || 0,
        botDetected: data.botDetected || false,
        lastStatus: data.lastStatus || (data.enabled ? 'active' : 'inactive'),
      };
      ui.statusContainer.classList.remove('loading');
      updateUI(fullData);
    });
  }

  // --- Main Execution ---
  applyTranslations();
  setupLanguageSwitcher();
  setupTheme();
  setupEventListeners();
  loadInitialData();
});
