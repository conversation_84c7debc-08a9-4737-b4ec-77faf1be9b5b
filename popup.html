<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>reCAPTCHA Audio Solver</title>
  <link rel="stylesheet" href="styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Product+Sans:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 id="title" data-lang-key="title">reCAPTCHA Audio Solver</h1>
      <div class="toggle-container">
        <label class="switch">
          <input type="checkbox" id="solverToggle">
          <span class="slider round"></span>
        </label>
        <span id="toggleStatus" data-lang-key="toggleInactive"></span>
      </div>
    </div>

    <div class="status-container loading">
      <div id="statusLabel" class="status-label" data-lang-key="statusLabel"></div>
      <div id="status" class="status-value" data-lang-key="loading"></div>
    </div>



    <div class="stats-container">
      <div class="stat">
        <div id="successLabel" class="stat-label" data-lang-key="successLabel"></div>
        <div id="solveCount" class="stat-value">0</div>
      </div>
      <div class="stat">
        <div id="failLabel" class="stat-label" data-lang-key="failLabel"></div>
        <div id="failCount" class="stat-value">0</div>
      </div>
    </div>

    <div class="footer">
      <div class="version">v3.8.1</div>
      <div class="author" data-lang-key="authorCredit"></div>
    </div>

    <div class="bottom-container">
      <div class="lang-selector">
        <button id="langEN" class="lang-btn">EN</button>
        <button id="langID" class="lang-btn">ID</button>
        <button id="langES" class="lang-btn">ES</button>
      </div>
      <div class="theme-toggle" id="themeToggle">
        <svg class="theme-icon" id="themeIcon" viewBox="0 0 24 24">
          <!-- Icon path will be set by JS -->
        </svg>
      </div>
    </div>
  </div>
  <script src="constants.js"></script>
  <script src="lang.js"></script>
  <script src="popup.js"></script>
</body>
</html>
